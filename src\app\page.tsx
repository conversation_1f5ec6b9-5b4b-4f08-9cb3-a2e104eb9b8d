"use client"

import { ThemeToggle } from "@/components/theme-toggle"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DotScreenShader } from "@/components/ui/dot-shader-background"

export default function Home() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Theme Toggle Button */}
      <div className="absolute top-4 right-4 z-20">
        <ThemeToggle />
      </div>

      {/* Advanced Shader Background */}
      <DotScreenShader />

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen">
        <div className="text-center space-y-6 px-4">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
            FDX SMART WORK 2.0
          </h1>
          <div className="space-y-4 max-w-2xl mx-auto">
            <p className="text-xl md:text-2xl text-muted-foreground">
              欢迎回到富鼎翔数智化工作管理系统2.0。
            </p>
            <p className="text-lg md:text-xl text-muted-foreground">
              高效产线已就绪，登录开启今日精准排程。
            </p>
          </div>
          <div className="mt-8">
            <Button
              size="lg"
              className="px-8 py-3 text-lg font-semibold"
              onClick={() => window.location.href = '/profile'}
            >
              登录
            </Button>
          </div>
        </div>
      </div>


    </div>
  )
}


