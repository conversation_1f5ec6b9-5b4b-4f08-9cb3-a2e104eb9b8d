"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@/contexts/user-context"

export default function Home() {
  const { user, isAuthenticated, isLoading } = useUser();
  const router = useRouter();

  useEffect(() => {
    // 如果用户已登录，重定向到工作页面
    if (isAuthenticated && user) {
      const workPage = user.position === '总指挥' ? 'boss' :
                      user.position === '管理员' ? 'manager' : 'lab';
      console.log('✅ [首页] 用户已登录，重定向到工作页面:', workPage);
      router.replace(`/${workPage}`);
    } else {
      // 如果用户未登录，重定向到登录页面
      console.log('🔄 [首页] 用户未登录，重定向到登录页面');
      router.replace('/auth/login');
    }
  }, [isAuthenticated, user, router]);

  // 显示加载状态，避免闪烁
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">正在加载...</p>
      </div>
    </div>
  );
}


