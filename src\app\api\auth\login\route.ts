import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    账号: string;
    姓名: string;
    部门: string;
    工作页面: string;
    职称: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const { email, password }: LoginRequest = await request.json();

    console.log('🔐 [登录API] 收到登录请求:', { email, password: '***' });

    if (!email || !password) {
      return NextResponse.json({
        success: false,
        message: '请填写账号和密码'
      } as LoginResponse);
    }

    // 创建Supabase客户端
    const supabase = createClient();

    // 查询用户资料表进行身份验证
    const { data: users, error } = await supabase
      .from('用户资料')
      .select('*')
      .eq('账号', email)
      .eq('密码', password)
      .limit(1);

    if (error) {
      console.error('❌ [登录API] 数据库查询错误:', error);
      return NextResponse.json({
        success: false,
        message: '登录验证失败，请重试'
      } as LoginResponse);
    }

    if (!users || users.length === 0) {
      console.log('❌ [登录API] 账号或密码错误');
      return NextResponse.json({
        success: false,
        message: '账号或密码错误'
      } as LoginResponse);
    }

    const user = users[0];
    console.log('✅ [登录API] 登录成功:', { 
      账号: user.账号, 
      姓名: user.姓名,
      部门: user.部门 
    });

    // 返回登录成功响应
    return NextResponse.json({
      success: true,
      message: '登录成功',
      user: {
        id: user.id,
        账号: user.账号,
        姓名: user.姓名,
        部门: user.部门,
        工作页面: user.工作页面 || 'lab',
        职称: user.职称 || '化验师'
      }
    } as LoginResponse);

  } catch (error) {
    console.error('❌ [登录API] 服务器错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器错误，请稍后重试'
    } as LoginResponse);
  }
}
