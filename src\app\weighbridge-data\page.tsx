"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  ArrowLeft, Scale, Search, Filter, Download, Calendar,
  Truck, Package, User, Clock, TrendingUp, TrendingDown,
  BarChart3, FileText, Eye, Edit, Trash2
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ThemeToggle } from "@/components/theme-toggle";
import { HamburgerMenu } from "@/components/hamburger-menu";
import { Footer } from "@/components/ui/footer";

// 地磅数据接口
interface WeighbridgeRecord {
  id: string;
  recordNumber: string;
  vehicleNumber: string;
  driverName: string;
  driverPhone: string;
  materialType: string;
  supplier: string;
  destination: string;
  grossWeight: number; // 毛重
  tareWeight: number;  // 皮重
  netWeight: number;   // 净重
  weighInTime: string;
  weighOutTime?: string;
  operator: string;
  status: 'weigh_in' | 'weigh_out' | 'completed' | 'cancelled';
  remarks?: string;
  attachments?: string[];
  moistureContent?: number; // 水分含量
  qualityGrade?: string;    // 质量等级
  unitPrice?: number;       // 单价
  totalAmount?: number;     // 总金额
}

// 地磅统计接口
interface WeighbridgeStats {
  totalRecords: number;
  todayRecords: number;
  totalWeight: number;
  todayWeight: number;
  averageWeight: number;
  inProgress: number;
  completed: number;
  totalValue: number;
}

export default function WeighbridgeDataPage() {
  const router = useRouter();
  const [records, setRecords] = useState<WeighbridgeRecord[]>([]);
  const [stats, setStats] = useState<WeighbridgeStats | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [selectedMaterial, setSelectedMaterial] = useState("");
  const [dateRange, setDateRange] = useState("");
  const [loading, setLoading] = useState(true);

  // 模拟数据加载
  useEffect(() => {
    const generateMockRecords = (): WeighbridgeRecord[] => {
      const data: WeighbridgeRecord[] = [];
      const vehicleNumbers = ['云A12345', '云B67890', '云C11111', '云D22222', '云E33333', '云F44444'];
      const drivers = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅', '刘师傅'];
      const phones = ['13888888888', '13999999999', '13777777777', '13666666666', '13555555555'];
      const materials = ['铅锌原矿', '铜矿石', '铁矿石', '煤炭', '石灰石', '精矿粉'];
      const suppliers = ['矿业公司A', '矿业公司B', '矿业公司C', '运输公司D', '贸易公司E'];
      const destinations = ['选矿厂', '冶炼厂', '仓库A', '仓库B', '中转站'];
      const operators = ['操作员A', '操作员B', '操作员C'];
      const statuses: ('weigh_in' | 'weigh_out' | 'completed' | 'cancelled')[] = 
        ['weigh_in', 'weigh_out', 'completed', 'cancelled'];
      const qualityGrades = ['A级', 'B级', 'C级', 'D级'];

      for (let i = 0; i < 100; i++) {
        const weighInTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const weighOutTime = ['weigh_out', 'completed'].includes(status) ? 
          new Date(weighInTime.getTime() + Math.random() * 4 * 60 * 60 * 1000) : undefined;
        
        const tareWeight = Math.round((8 + Math.random() * 12) * 1000); // 8-20吨皮重
        const netWeight = Math.round((15 + Math.random() * 35) * 1000); // 15-50吨净重
        const grossWeight = tareWeight + netWeight;
        const materialType = materials[Math.floor(Math.random() * materials.length)];
        const unitPrice = Math.round((200 + Math.random() * 800) * 100) / 100; // 200-1000元/吨
        const totalAmount = Math.round((netWeight / 1000) * unitPrice * 100) / 100;

        data.push({
          id: `WB${weighInTime.getFullYear()}${String(weighInTime.getMonth() + 1).padStart(2, '0')}-${String(i % 1000).padStart(3, '0')}`,
          recordNumber: `WB-${weighInTime.getFullYear()}${String(weighInTime.getMonth() + 1).padStart(2, '0')}-${String(i % 1000 + 1).padStart(4, '0')}`,
          vehicleNumber: vehicleNumbers[Math.floor(Math.random() * vehicleNumbers.length)],
          driverName: drivers[Math.floor(Math.random() * drivers.length)],
          driverPhone: phones[Math.floor(Math.random() * phones.length)],
          materialType,
          supplier: suppliers[Math.floor(Math.random() * suppliers.length)],
          destination: destinations[Math.floor(Math.random() * destinations.length)],
          grossWeight,
          tareWeight,
          netWeight,
          weighInTime: weighInTime.toISOString(),
          weighOutTime: weighOutTime?.toISOString(),
          operator: operators[Math.floor(Math.random() * operators.length)],
          status,
          remarks: Math.random() > 0.7 ? '货物质量良好，符合标准' : undefined,
          attachments: Math.random() > 0.8 ? ['过磅单.pdf', '质检报告.pdf'] : undefined,
          moistureContent: materialType.includes('矿') ? Math.round((5 + Math.random() * 10) * 100) / 100 : undefined,
          qualityGrade: materialType.includes('矿') ? qualityGrades[Math.floor(Math.random() * qualityGrades.length)] : undefined,
          unitPrice,
          totalAmount
        });
      }
      
      return data.sort((a, b) => new Date(b.weighInTime).getTime() - new Date(a.weighInTime).getTime());
    };

    const mockRecords = generateMockRecords();
    setRecords(mockRecords);

    // 计算统计数据
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayRecords = mockRecords.filter(r => new Date(r.weighInTime) >= today);
    
    const totalRecords = mockRecords.length;
    const todayRecordsCount = todayRecords.length;
    const totalWeight = Math.round(mockRecords.reduce((sum, r) => sum + r.netWeight, 0) / 1000); // 转换为吨
    const todayWeight = Math.round(todayRecords.reduce((sum, r) => sum + r.netWeight, 0) / 1000);
    const averageWeight = totalRecords > 0 ? Math.round(totalWeight / totalRecords * 100) / 100 : 0;
    const inProgress = mockRecords.filter(r => ['weigh_in', 'weigh_out'].includes(r.status)).length;
    const completed = mockRecords.filter(r => r.status === 'completed').length;
    const totalValue = Math.round(mockRecords.reduce((sum, r) => sum + (r.totalAmount || 0), 0) * 100) / 100;

    setStats({
      totalRecords,
      todayRecords: todayRecordsCount,
      totalWeight,
      todayWeight,
      averageWeight,
      inProgress,
      completed,
      totalValue
    });

    setLoading(false);
  }, []);

  // 筛选记录
  const filteredRecords = records.filter(record => {
    const matchesSearch = searchTerm === "" || 
      record.recordNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.vehicleNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.materialType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = selectedStatus === "" || record.status === selectedStatus;
    const matchesMaterial = selectedMaterial === "" || record.materialType === selectedMaterial;
    
    let matchesDate = true;
    if (dateRange) {
      const recordDate = new Date(record.weighInTime);
      const today = new Date();
      switch (dateRange) {
        case 'today':
          matchesDate = recordDate.toDateString() === today.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDate = recordDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
          matchesDate = recordDate >= monthAgo;
          break;
      }
    }
    
    return matchesSearch && matchesStatus && matchesMaterial && matchesDate;
  });

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'weigh_in': return 'bg-blue-100 text-blue-800';
      case 'weigh_out': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'weigh_in': return '已进磅';
      case 'weigh_out': return '已出磅';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  // 导出数据
  const exportData = () => {
    const csvContent = [
      ['记录号', '车牌号', '司机', '物料类型', '供应商', '毛重(kg)', '皮重(kg)', '净重(kg)', '进磅时间', '出磅时间', '状态', '金额(元)'],
      ...filteredRecords.map(record => [
        record.recordNumber,
        record.vehicleNumber,
        record.driverName,
        record.materialType,
        record.supplier,
        record.grossWeight,
        record.tareWeight,
        record.netWeight,
        new Date(record.weighInTime).toLocaleString(),
        record.weighOutTime ? new Date(record.weighOutTime).toLocaleString() : '',
        getStatusText(record.status),
        record.totalAmount || ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `地磅数据_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-lg font-semibold">地磅数据</h1>
          </div>
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <HamburgerMenu />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 统计卡片 */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Scale className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">总记录</p>
                    <p className="text-lg font-bold">{stats.totalRecords}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">今日记录</p>
                    <p className="text-lg font-bold">{stats.todayRecords}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Package className="h-4 w-4 text-purple-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">总重量(吨)</p>
                    <p className="text-lg font-bold">{stats.totalWeight}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-orange-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">今日重量(吨)</p>
                    <p className="text-lg font-bold">{stats.todayWeight}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4 text-yellow-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">平均重量(吨)</p>
                    <p className="text-lg font-bold">{stats.averageWeight}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">进行中</p>
                    <p className="text-lg font-bold">{stats.inProgress}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Truck className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">已完成</p>
                    <p className="text-lg font-bold">{stats.completed}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingDown className="h-4 w-4 text-red-500" />
                  <div>
                    <p className="text-xs text-muted-foreground">总金额(万元)</p>
                    <p className="text-lg font-bold">{Math.round(stats.totalValue / 10000 * 100) / 100}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs defaultValue="list" className="space-y-4">
          <TabsList>
            <TabsTrigger value="list">过磅记录</TabsTrigger>
            <TabsTrigger value="statistics">统计分析</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            {/* 数据筛选 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  数据筛选
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="search">搜索</Label>
                    <Input
                      id="search"
                      placeholder="记录号/车牌/司机/物料/供应商"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">过磅状态</Label>
                    <select
                      id="status"
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="">全部状态</option>
                      <option value="weigh_in">已进磅</option>
                      <option value="weigh_out">已出磅</option>
                      <option value="completed">已完成</option>
                      <option value="cancelled">已取消</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="material">物料类型</Label>
                    <select
                      id="material"
                      value={selectedMaterial}
                      onChange={(e) => setSelectedMaterial(e.target.value)}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="">全部物料</option>
                      <option value="铅锌原矿">铅锌原矿</option>
                      <option value="铜矿石">铜矿石</option>
                      <option value="铁矿石">铁矿石</option>
                      <option value="煤炭">煤炭</option>
                      <option value="石灰石">石灰石</option>
                      <option value="精矿粉">精矿粉</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateRange">时间范围</Label>
                    <select
                      id="dateRange"
                      value={dateRange}
                      onChange={(e) => setDateRange(e.target.value)}
                      className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                    >
                      <option value="">全部时间</option>
                      <option value="today">今天</option>
                      <option value="week">最近一周</option>
                      <option value="month">最近一月</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label>&nbsp;</Label>
                    <Button onClick={exportData} className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      导出数据
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 过磅记录表格 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Filter className="h-5 w-5" />
                    过磅记录
                  </span>
                  <Badge variant="secondary">
                    共 {filteredRecords.length} 条记录
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>记录号</TableHead>
                        <TableHead>车牌号</TableHead>
                        <TableHead>司机</TableHead>
                        <TableHead>物料类型</TableHead>
                        <TableHead>供应商</TableHead>
                        <TableHead>毛重(kg)</TableHead>
                        <TableHead>皮重(kg)</TableHead>
                        <TableHead>净重(kg)</TableHead>
                        <TableHead>进磅时间</TableHead>
                        <TableHead>出磅时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>金额(元)</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRecords.slice(0, 20).map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-medium">{record.recordNumber}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Truck className="h-3 w-3 text-blue-500" />
                              <span>{record.vehicleNumber}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              <span>{record.driverName}</span>
                            </div>
                          </TableCell>
                          <TableCell>{record.materialType}</TableCell>
                          <TableCell>{record.supplier}</TableCell>
                          <TableCell className="text-right">{record.grossWeight.toLocaleString()}</TableCell>
                          <TableCell className="text-right">{record.tareWeight.toLocaleString()}</TableCell>
                          <TableCell className="text-right font-medium">{record.netWeight.toLocaleString()}</TableCell>
                          <TableCell>{new Date(record.weighInTime).toLocaleString()}</TableCell>
                          <TableCell>
                            {record.weighOutTime ? new Date(record.weighOutTime).toLocaleString() : '-'}
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(record.status)}>
                              {getStatusText(record.status)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right font-medium">
                            {record.totalAmount ? `¥${record.totalAmount.toLocaleString()}` : '-'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Button size="sm" variant="ghost">
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="ghost">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="ghost">
                                <FileText className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="statistics">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  统计分析
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">统计分析功能开发中...</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* 底部签名 */}
      <Footer />
    </div>
  );
}
