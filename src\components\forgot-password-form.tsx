"use client";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useCallback, useMemo } from "react";

interface ForgotPasswordRequest {
  email: string;
}

interface ForgotPasswordResponse {
  success: boolean;
  message: string;
}

export function ForgotPasswordForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isButtonPressed, setIsButtonPressed] = useState(false);
  const router = useRouter();

  // 表单验证
  const isFormValid = useMemo(() => {
    return email.trim().length > 0;
  }, [email]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // 立即设置按钮按下状态，提供即时反馈
    setIsButtonPressed(true);
    setTimeout(() => setIsButtonPressed(false), 150);

    if (!isFormValid) {
      setError("请输入账号或邮箱");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    console.log('🔄 [忘记密码] 开始重置流程', { email });

    try {
      // 调用忘记密码API
      const forgotPasswordRequest: ForgotPasswordRequest = {
        email: email.trim(),
      };

      console.log('📤 [忘记密码] 发送重置请求', forgotPasswordRequest);

      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(forgotPasswordRequest),
      });

      console.log('📥 [忘记密码] 收到响应', { status: response.status, ok: response.ok });

      const result: ForgotPasswordResponse = await response.json();
      console.log('📋 [忘记密码] 解析响应数据', result);

      if (!result.success) {
        console.error('❌ [忘记密码] 重置失败', result.message);
        setError(result.message || "密码重置失败，请重试");
        return;
      }

      // 重置成功
      console.log('✅ [忘记密码] 重置请求成功', result.message);
      setSuccess(result.message);
      
      // 3秒后自动跳转到登录页面
      setTimeout(() => {
        router.push('/auth/login?message=请查收邮箱中的密码重置链接');
      }, 3000);

    } catch (error: unknown) {
      console.error('❌ [忘记密码] 请求错误:', error);
      setError("网络错误，请检查连接后重试");
    } finally {
      setIsLoading(false);
    }
  }, [email, isFormValid, router]);

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">忘记密码</CardTitle>
          <CardDescription>
            输入您的账号或邮箱，我们将发送密码重置链接
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="grid gap-2">
                <Label htmlFor="email">账号或邮箱</Label>
                <Input
                  id="email"
                  type="text"
                  placeholder="请输入账号或邮箱地址"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              
              {error && <p className="text-sm text-red-500">{error}</p>}
              {success && <p className="text-sm text-green-600">{success}</p>}
              
              <Button
                type="submit"
                className={`w-full transition-all duration-150 ${
                  isButtonPressed ? 'scale-95' : 'scale-100'
                } ${
                  !isFormValid ? 'opacity-50' : 'opacity-100'
                }`}
                disabled={isLoading || !isFormValid}
              >
                {isLoading ? "发送中..." : "发送重置链接"}
              </Button>
            </div>
            <div className="mt-4 text-center text-sm">
              记起密码了？{" "}
              <Link
                href="/auth/login"
                className="underline underline-offset-4 hover:text-primary"
              >
                返回登录
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
