"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft, Plus, FileText, Calendar, DollarSign,
  CheckCircle, Clock, AlertTriangle, User, Send,
  RefreshCw, Search, Filter, Edit, Trash2, Eye,
  Download, Upload, BarChart3, TrendingUp, Package
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ThemeToggle } from "@/components/theme-toggle";
import { HamburgerMenu } from "@/components/hamburger-menu";
import { Footer } from "@/components/ui/footer";
import { PurchaseRequest, PurchaseStatus, PurchaseRequestFilter } from "@/types/purchase";

// 采购申请统计接口
interface PurchaseStats {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  completedRequests: number;
  totalAmount: number;
  avgProcessingTime: number;
}

export default function PurchaseManagementPage() {
  const router = useRouter();
  const [requests, setRequests] = useState<PurchaseRequest[]>([]);
  const [stats, setStats] = useState<PurchaseStats | null>(null);
  const [filter, setFilter] = useState<PurchaseRequestFilter>({
    searchTerm: "",
    status: "",
    dateRange: {},
    applicant: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<PurchaseRequest | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);

  // 加载采购申请数据
  const loadPurchaseRequests = async () => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams();
      if (filter.searchTerm) queryParams.append('search', filter.searchTerm);
      if (filter.status && filter.status !== 'all') queryParams.append('status', filter.status);
      if (filter.applicant && filter.applicant !== 'all') queryParams.append('applicant', filter.applicant);

      const response = await fetch(`/api/purchase-requests?${queryParams.toString()}`);
      const result = await response.json();

      if (result.success) {
        setRequests(result.data);
        calculateStats(result.data);
      } else {
        console.error('加载数据失败:', result.error);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 计算统计数据
  const calculateStats = (data: PurchaseRequest[]) => {
    const pendingRequests = data.filter(r => r.status === PurchaseStatus.PENDING);
    const approvedRequests = data.filter(r => r.status === PurchaseStatus.APPROVED);
    const rejectedRequests = data.filter(r => r.status === PurchaseStatus.REJECTED);
    const completedRequests = data.filter(r => r.status === PurchaseStatus.COMPLETED);

    setStats({
      totalRequests: data.length,
      pendingRequests: pendingRequests.length,
      approvedRequests: approvedRequests.length,
      rejectedRequests: rejectedRequests.length,
      completedRequests: completedRequests.length,
      totalAmount: data.reduce((sum, r) => sum + (r.quantity * 100), 0), // 假设单价100元
      avgProcessingTime: 3.5 // 假设平均处理时间
    });
  };

  // 初始加载数据
  useEffect(() => {
    loadPurchaseRequests();
  }, [filter]);

  // 更新申请状态
  const updateRequestStatus = async (id: number, status: string) => {
    try {
      const response = await fetch('/api/purchase-requests', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, status }),
      });

      const result = await response.json();
      if (result.success) {
        loadPurchaseRequests(); // 重新加载数据
      } else {
        console.error('更新状态失败:', result.error);
      }
    } catch (error) {
      console.error('更新状态失败:', error);
    }
  };

  // 删除申请
  const deleteRequest = async (id: number) => {
    if (!confirm('确定要删除这个采购申请吗？')) return;

    try {
      const response = await fetch(`/api/purchase-requests?id=${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        loadPurchaseRequests(); // 重新加载数据
      } else {
        console.error('删除失败:', result.error);
      }
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 导出数据
  const exportData = () => {
    const csvContent = [
      ['ID', '日期', '申请者', '品名', '数量', '用途', '期望完成日期', '状态'],
      ...requests.map(r => [
        r.id,
        r.date,
        r.applicant,
        r.itemName,
        r.quantity,
        r.purpose,
        r.expectedDate || '',
        r.status
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `采购申请_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case PurchaseStatus.PENDING: return 'bg-yellow-100 text-yellow-800';
      case PurchaseStatus.APPROVED: return 'bg-green-100 text-green-800';
      case PurchaseStatus.REJECTED: return 'bg-red-100 text-red-800';
      case PurchaseStatus.IN_PROGRESS: return 'bg-blue-100 text-blue-800';
      case PurchaseStatus.COMPLETED: return 'bg-purple-100 text-purple-800';
      case PurchaseStatus.CANCELLED: return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取申请者列表
  const applicants = Array.from(new Set(requests.map(r => r.applicant).filter(a => a)));

  const updateFilter = (key: keyof PurchaseRequestFilter, value: any) => {
    setFilter(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-lg font-semibold">采购管理</h1>
          </div>
          <div className="flex flex-1 items-center justify-end space-x-2">
            <ThemeToggle />
            <HamburgerMenu />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6 space-y-6">
        {/* 统计概览 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总申请数</p>
                    <p className="text-2xl font-bold">{stats.totalRequests}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">待处理</p>
                    <p className="text-2xl font-bold text-yellow-600">{stats.pendingRequests}</p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">已批准</p>
                    <p className="text-2xl font-bold text-green-600">{stats.approvedRequests}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">已拒绝</p>
                    <p className="text-2xl font-bold text-red-600">{stats.rejectedRequests}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">已完成</p>
                    <p className="text-2xl font-bold text-purple-600">{stats.completedRequests}</p>
                  </div>
                  <Package className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总金额</p>
                    <p className="text-2xl font-bold">{(stats.totalAmount / 10000).toFixed(1)}</p>
                    <p className="text-xs text-muted-foreground">万元</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-cyan-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均处理</p>
                    <p className="text-2xl font-bold">{stats.avgProcessingTime}</p>
                    <p className="text-xs text-muted-foreground">天</p>
                  </div>
                  <Calendar className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 功能操作区 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
          <div>
            <h2 className="text-xl font-semibold">采购申请管理</h2>
            <p className="text-sm text-muted-foreground">查看、管理和处理所有采购申请</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportData}
            >
              <Download className="h-4 w-4 mr-1" />
              导出数据
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadPurchaseRequests}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Button
              size="sm"
              onClick={() => router.push('/purchase-request')}
            >
              <Plus className="h-4 w-4 mr-1" />
              新建申请
            </Button>
          </div>
        </div>
        {/* 筛选和搜索 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>筛选条件</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">搜索</Label>
                <Input
                  id="search"
                  placeholder="申请者/品名/用途"
                  value={filter.searchTerm}
                  onChange={(e) => updateFilter('searchTerm', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">状态筛选</Label>
                <Select value={filter.status} onValueChange={(value) => updateFilter('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value={PurchaseStatus.PENDING}>待处理</SelectItem>
                    <SelectItem value={PurchaseStatus.APPROVED}>已批准</SelectItem>
                    <SelectItem value={PurchaseStatus.REJECTED}>已拒绝</SelectItem>
                    <SelectItem value={PurchaseStatus.IN_PROGRESS}>进行中</SelectItem>
                    <SelectItem value={PurchaseStatus.COMPLETED}>已完成</SelectItem>
                    <SelectItem value={PurchaseStatus.CANCELLED}>已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="applicant">申请者筛选</Label>
                <Select value={filter.applicant} onValueChange={(value) => updateFilter('applicant', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择申请者" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部申请者</SelectItem>
                    {applicants.map((applicant) => (
                      <SelectItem key={applicant} value={applicant}>
                        {applicant}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button
                  variant="outline"
                  onClick={() => setFilter({
                    searchTerm: "",
                    status: "",
                    dateRange: {},
                    applicant: ""
                  })}
                  className="w-full"
                >
                  清除筛选
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 采购申请列表 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>采购申请列表</span>
              </span>
              <Badge variant="secondary">
                共 {requests.length} 条记录
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>申请日期</TableHead>
                    <TableHead>申请者</TableHead>
                    <TableHead>品名</TableHead>
                    <TableHead>数量</TableHead>
                    <TableHead>用途</TableHead>
                    <TableHead>期望完成日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {requests.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                        暂无采购申请数据
                      </TableCell>
                    </TableRow>
                  ) : (
                    requests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell className="font-medium">{request.id}</TableCell>
                        <TableCell>{request.date}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{request.applicant}</span>
                          </div>
                        </TableCell>
                        <TableCell>{request.itemName}</TableCell>
                        <TableCell>{request.quantity}</TableCell>
                        <TableCell>
                          <div className="max-w-32 truncate" title={request.purpose}>
                            {request.purpose}
                          </div>
                        </TableCell>
                        <TableCell>{request.expectedDate || '-'}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(request.status)}>
                            {request.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setSelectedRequest(request)}
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Select
                              value={request.status}
                              onValueChange={(value) => updateRequestStatus(request.id, value)}
                            >
                              <SelectTrigger className="w-24 h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value={PurchaseStatus.PENDING}>待处理</SelectItem>
                                <SelectItem value={PurchaseStatus.APPROVED}>已批准</SelectItem>
                                <SelectItem value={PurchaseStatus.REJECTED}>已拒绝</SelectItem>
                                <SelectItem value={PurchaseStatus.IN_PROGRESS}>进行中</SelectItem>
                                <SelectItem value={PurchaseStatus.COMPLETED}>已完成</SelectItem>
                                <SelectItem value={PurchaseStatus.CANCELLED}>已取消</SelectItem>
                              </SelectContent>
                            </Select>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => deleteRequest(request.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </main>

      <Footer />
    </div>
  );
}

