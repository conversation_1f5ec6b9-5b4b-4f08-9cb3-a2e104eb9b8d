"use client"

import React, { createContext, useContext, useState, useEffect } from 'react';
import { AvatarCacheService } from '@/lib/avatar-cache';

// 用户信息接口
export interface UserInfo {
  id: string;
  username: string;
  name: string;
  position: string;
  department: string;
  phone: string;
  wechat: string;
  points: number;
  avatar?: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
}

// 用户上下文接口
interface UserContextType {
  user: UserInfo | null;
  setUser: (user: UserInfo | null) => void;
  updateUser: (user: UserInfo) => void;
  refreshUser: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

// 创建用户上下文
const UserContext = createContext<UserContextType | undefined>(undefined);

// 默认用户数据（模拟登录用户）
const defaultUser: UserInfo = {
  id: "user_001",
  username: "z<PERSON><PERSON>",
  name: "张三",
  position: "高级化验员",
  department: "化验室",
  phone: "138-8888-8888",
  wechat: "zhangsan_fdx",
  points: 1250,
  avatar: undefined
};

// 用户API服务
const userAPI = {
  async getUserById(userId: string): Promise<UserInfo | null> {
    try {
      const response = await fetch(`/api/users?id=${userId}`);
      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        console.error('获取用户信息失败:', result.error);
        return null;
      }
    } catch (error) {
      console.error('用户API调用失败:', error);
      return null;
    }
  },

  async updateUser(userId: string, updates: Partial<UserInfo>): Promise<UserInfo | null> {
    try {
      const response = await fetch('/api/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: userId, ...updates }),
      });

      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        console.error('更新用户信息失败:', result.error);
        return null;
      }
    } catch (error) {
      console.error('更新用户API调用失败:', error);
      return null;
    }
  }
};

// 用户上下文提供者组件
export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从API获取用户信息（使用混合存储策略）
  const loadUserFromAPI = async (userId: string) => {
    try {
      setError(null);

      // 1. 尝试从缓存获取头像
      const cachedAvatar = await AvatarCacheService.getUserAvatar(userId);

      // 2. 从API获取用户基本信息
      const userData = await userAPI.getUserById(userId);

      if (userData) {
        // 3. 优先使用缓存的头像，降级到API返回的头像
        const finalAvatar = cachedAvatar || userData.avatar_url || userData.avatar;

        // 4. 确保avatar字段兼容性
        const userWithAvatar = {
          ...userData,
          avatar: finalAvatar,
          avatar_url: finalAvatar
        };

        setUser(userWithAvatar);

        // 5. 更新本地存储
        localStorage.setItem('fdx_user', JSON.stringify(userWithAvatar));
        localStorage.setItem('fdx_current_user_id', userId);

        // 6. 如果有新的头像URL，更新缓存
        if (userData.avatar_url && userData.avatar_url !== cachedAvatar) {
          AvatarCacheService.setCachedAvatar(userId, userData.avatar_url, 'supabase');
        }
      } else {
        throw new Error('用户信息不存在');
      }
    } catch (error) {
      console.error('从API加载用户信息失败:', error);
      setError(error instanceof Error ? error.message : '加载用户信息失败');
      // 降级到默认用户
      setUser(defaultUser);
      localStorage.setItem('fdx_user', JSON.stringify(defaultUser));
    }
  };

  // 刷新用户信息
  const refreshUser = async () => {
    const currentUserId = localStorage.getItem('fdx_current_user_id') || defaultUser.id;
    await loadUserFromAPI(currentUserId);
  };

  useEffect(() => {
    const loadUser = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 1. 尝试从本地存储获取当前用户ID
        const savedUserId = localStorage.getItem('fdx_current_user_id');
        const savedUser = localStorage.getItem('fdx_user');

        if (savedUserId) {
          // 2. 从API获取最新用户信息
          await loadUserFromAPI(savedUserId);
        } else if (savedUser) {
          // 3. 降级：使用本地缓存的用户信息
          const parsedUser = JSON.parse(savedUser);
          setUser(parsedUser);
          localStorage.setItem('fdx_current_user_id', parsedUser.id);
        } else {
          // 4. 最后降级：使用默认用户并尝试从API获取
          await loadUserFromAPI(defaultUser.id);
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
        setError('加载用户信息失败');
        setUser(defaultUser);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  // 更新用户信息时同步到API和本地存储（使用混合存储策略）
  const updateUser = async (newUser: UserInfo) => {
    try {
      setError(null);

      // 1. 更新到API
      const updatedUser = await userAPI.updateUser(newUser.id, newUser);

      if (updatedUser) {
        // 2. 确保avatar字段兼容性
        const userWithAvatar = {
          ...updatedUser,
          avatar: updatedUser.avatar_url || updatedUser.avatar
        };

        // 3. 更新本地状态
        setUser(userWithAvatar);

        // 4. 更新本地缓存
        localStorage.setItem('fdx_user', JSON.stringify(userWithAvatar));
        localStorage.setItem('fdx_current_user_id', userWithAvatar.id);

        // 5. 更新头像缓存
        if (userWithAvatar.avatar) {
          AvatarCacheService.setCachedAvatar(userWithAvatar.id, userWithAvatar.avatar, 'supabase');
        }
      } else {
        throw new Error('更新用户信息失败');
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
      setError(error instanceof Error ? error.message : '更新用户信息失败');

      // 降级：仅更新本地状态和缓存
      setUser(newUser);
      localStorage.setItem('fdx_user', JSON.stringify(newUser));

      // 即使API失败，也要更新本地头像缓存
      if (newUser.avatar) {
        AvatarCacheService.setCachedAvatar(newUser.id, newUser.avatar, 'local');
      }
    }
  };

  // 设置用户（用于登录等场景）
  const setUserData = (newUser: UserInfo | null) => {
    setUser(newUser);
    if (newUser) {
      localStorage.setItem('fdx_user', JSON.stringify(newUser));
      localStorage.setItem('fdx_current_user_id', newUser.id);
    } else {
      localStorage.removeItem('fdx_user');
      localStorage.removeItem('fdx_current_user_id');
    }
  };

  return (
    <UserContext.Provider value={{
      user,
      setUser: setUserData,
      updateUser,
      refreshUser,
      isLoading,
      error
    }}>
      {children}
    </UserContext.Provider>
  );
}

// 使用用户上下文的Hook
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
